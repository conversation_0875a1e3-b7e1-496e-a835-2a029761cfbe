# Facebook Web Crawler - Maximum Stealth Edition

A sophisticated Facebook web crawler built with Puppeteer and advanced anti-detection measures. This crawler is designed to operate with maximum stealth capabilities, including WebRTC leak prevention, fingerprint randomization, and human-like behavior simulation.

## 🚀 Features

### Maximum Stealth Capabilities
- **WebRTC IP Leak Prevention** - Completely blocks WebRTC to prevent IP leaks
- **Fingerprint Randomization** - Randomizes canvas, WebGL, and navigator fingerprints
- **User Agent Rotation** - Uses realistic, rotating user agents
- **Viewport Randomization** - Random screen resolutions and viewport sizes
- **Human-like Behavior** - Realistic mouse movements, typing patterns, and scrolling
- **Advanced Request Filtering** - Blocks trackers, ads, and unnecessary resources

### Anti-Detection Measures
- **Navigator Property Override** - Hides automation indicators
- **Chrome Runtime Spoofing** - Mimics real Chrome browser behavior
- **Request Rate Limiting** - Prevents detection through request patterns
- **Random Delays** - Human-like timing between actions
- **Proxy Support** - IP rotation through proxy servers
- **Plugin System** - Modular stealth plugins

### Cookie Authentication System
- **Persistent Sessions** - Save and reuse login cookies
- **Automatic Cookie Management** - Auto-save after login, auto-load on startup
- **Manual Cookie Injection** - Copy cookies from browser and inject manually
- **Cookie Validation** - Verify cookie validity before use
- **Multiple Formats** - Export cookies in JSON or Netscape format
- **Cookie File Management** - List, delete, and organize cookie files

## 📦 Installation

```bash
# Clone or download the project
cd WebCrawler

# Install dependencies (already done)
pnpm install
```

## 🛠️ Configuration

The crawler is highly configurable through `src/config.js`:

### Browser Options
- Headless mode configuration
- Advanced Chrome flags for stealth
- WebRTC and WebGL blocking
- Memory and performance optimization

### Stealth Settings
- User agent rotation
- Viewport randomization
- Fingerprint spoofing
- Navigator property overrides

### Rate Limiting
- Requests per minute limits
- Burst protection
- Cooldown periods

### Proxy Configuration
```javascript
proxy: {
  enabled: true,
  servers: [
    { host: 'proxy1.example.com', port: 8080, username: 'user', password: 'pass' },
    { host: 'proxy2.example.com', port: 8080, username: 'user', password: 'pass' }
  ],
  rotation: true
}
```

### Cookie Configuration
```javascript
cookies: {
  enabled: true,
  directory: './cookies',
  defaultFile: 'facebook_cookies.json',
  autoSave: true,    // Save cookies after successful login
  autoLoad: true,    // Load cookies on initialization
  validateOnLoad: true,  // Validate cookies when loading
  maxAge: 7 * 24 * 60 * 60 * 1000  // 7 days
}
```

## 🚦 Usage

### Basic Usage

```javascript
const { FacebookCrawler } = require('./index');

async function crawl() {
  const crawler = new FacebookCrawler();

  try {
    // Initialize with stealth configuration
    await crawler.init();

    // Navigate to Facebook
    await crawler.navigateToFacebook();

    // Login (optional)
    await crawler.login('<EMAIL>', 'your-password');

    // Your crawling logic here
    // ...

  } finally {
    await crawler.close();
  }
}
```

### Cookie Authentication Usage

```javascript
// Method 1: Automatic cookie authentication
const crawler = new FacebookCrawler({
  cookies: {
    enabled: true,
    autoLoad: true,    // Automatically load saved cookies
    autoSave: true     // Save cookies after login
  }
});

await crawler.init(); // Will automatically try to authenticate with cookies

// Method 2: Manual cookie authentication
await crawler.authenticateWithCookies('my_cookies.json');

// Method 3: Manual cookie injection (copy from browser)
const cookies = [
  { name: 'c_user', value: 'your_user_id', domain: '.facebook.com', path: '/' },
  { name: 'xs', value: 'your_session_token', domain: '.facebook.com', path: '/' }
  // ... more cookies
];
await crawler.injectCookiesManually(cookies);
```

### Advanced Usage with Custom Configuration

```javascript
const crawler = new FacebookCrawler({
  browserOptions: {
    headless: false, // Visible browser for debugging
    devtools: true
  },
  delays: {
    pageLoad: { min: 3000, max: 6000 },
    typing: { min: 100, max: 200 }
  },
  proxy: {
    enabled: true,
    rotation: true
  }
});
```

## 🎯 Available Scripts

```bash
# Run the basic crawler
pnpm start

# Run with visible browser (development)
pnpm run dev

# Run with debugging
pnpm run debug

# Run examples
node example.js

# Run cookie examples
node cookie-examples.js
```

## 🔧 API Reference

### FacebookCrawler Class

#### Methods

- `init()` - Initialize browser with stealth configuration
- `navigateToFacebook()` - Navigate to Facebook homepage
- `login(email, password)` - Login with human-like behavior
- `checkIfLoggedIn()` - Check if user is currently logged in
- `screenshot(filename)` - Take screenshot for debugging
- `close()` - Close browser and cleanup

#### Cookie Methods

- `saveCookies(filename)` - Save current session cookies to file
- `loadCookies(filename)` - Load cookies from file and inject into session
- `authenticateWithCookies(filename)` - Load cookies and validate authentication
- `injectCookiesManually(cookiesArray)` - Inject cookies copied from browser
- `listCookieFiles()` - List all available cookie files
- `deleteCookieFile(filename)` - Delete a specific cookie file
- `exportCookies(format, filename)` - Export cookies in JSON or Netscape format

#### Properties

- `browser` - Puppeteer browser instance
- `page` - Current page instance
- `isLoggedIn` - Login status
- `stealthUtils` - Stealth utility methods
- `proxyManager` - Proxy management instance

### StealthUtils Class

#### Methods

- `humanType(page, selector, text)` - Human-like typing
- `humanClick(page, selector)` - Human-like clicking
- `randomScroll(page, options)` - Random scrolling simulation
- `randomDelay(min, max)` - Generate random delays
- `applyStealthMeasures(page, config)` - Apply all stealth measures

## 🛡️ Security Features

### WebRTC Protection
- Completely disables WebRTC connections
- Prevents IP leak through STUN servers
- Blocks WebRTC-based fingerprinting

### Fingerprint Randomization
- Canvas fingerprint noise injection
- WebGL parameter randomization
- Navigator property spoofing
- Plugin enumeration blocking

### Request Filtering
- Blocks tracking scripts and pixels
- Filters unnecessary resources
- Randomizes request timing
- Implements rate limiting

## 🍪 Cookie Authentication Guide

### Quick Start with Cookies

1. **First Time Setup** (Login once and save cookies):
```javascript
const crawler = new FacebookCrawler({ cookies: { enabled: true, autoSave: true } });
await crawler.init();
await crawler.login('<EMAIL>', 'your-password');
// Cookies are automatically saved after successful login
```

2. **Subsequent Uses** (Use saved cookies):
```javascript
const crawler = new FacebookCrawler({ cookies: { enabled: true, autoLoad: true } });
await crawler.init(); // Automatically loads and uses saved cookies
// No login required!
```

### Manual Cookie Copying from Browser

1. Open Facebook in your browser and login
2. Press F12 → Application tab → Cookies → facebook.com
3. Copy important cookies (c_user, xs, datr, sb, etc.)
4. Use manual injection:

```javascript
const cookies = [
  { name: 'c_user', value: 'your_user_id', domain: '.facebook.com', path: '/' },
  { name: 'xs', value: 'your_session_token', domain: '.facebook.com', path: '/' },
  // Add more cookies...
];
await crawler.injectCookiesManually(cookies);
```

### Cookie File Management

```javascript
// List available cookie files
const files = await crawler.listCookieFiles();

// Load specific cookie file
await crawler.loadCookies('backup_cookies.json');

// Export cookies
await crawler.exportCookies('json', 'my_export.json');
await crawler.exportCookies('netscape', 'my_export.txt');

// Delete old cookie files
await crawler.deleteCookieFile('old_cookies.json');
```

## 📝 Examples

See `example.js` for comprehensive usage examples including:
- Basic crawling setup
- Login automation
- Data collection
- Error handling
- Custom configurations

See `cookie-examples.js` for cookie authentication examples:
- Basic cookie authentication
- Manual cookie injection
- Cookie management operations
- Login once, use cookies forever workflow

## ⚠️ Important Notes

### Legal Compliance
- Always respect Facebook's Terms of Service
- Implement appropriate rate limiting
- Use for legitimate purposes only
- Consider Facebook's robots.txt

### Best Practices
- Always use proxies for production
- Implement proper error handling
- Monitor for detection indicators
- Use realistic delays between actions
- Rotate user agents and viewports

### Detection Avoidance
- Never run multiple instances simultaneously
- Implement random behavior patterns
- Monitor response codes for blocks
- Use residential proxies when possible
- Avoid predictable timing patterns

## 🔍 Debugging

### Screenshots
The crawler automatically saves screenshots to the `logs/` directory for debugging purposes.

### Console Logging
Enable detailed logging in the configuration:
```javascript
logging: {
  enabled: true,
  level: 'debug',
  console: true
}
```

### Browser DevTools
Run with visible browser and DevTools for debugging:
```bash
pnpm run dev
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is for educational purposes only. Use responsibly and in compliance with applicable laws and terms of service.

## ⚡ Performance Tips

- Use headless mode for production
- Enable request filtering to reduce bandwidth
- Implement proper proxy rotation
- Monitor memory usage for long-running sessions
- Use connection pooling for multiple instances

---

**Disclaimer**: This tool is for educational and research purposes only. Users are responsible for complying with Facebook's Terms of Service and applicable laws.
