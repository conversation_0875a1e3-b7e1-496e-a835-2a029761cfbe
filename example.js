/**
 * Example usage of Facebook Web Crawler
 * This file demonstrates how to use the crawler for various tasks
 */

const { FacebookCrawler } = require('./index');

async function exampleUsage() {
  const crawler = new FacebookCrawler({
    // Override default config if needed
    browserOptions: {
      headless: false, // Set to true for production
      devtools: false, // Set to true for debugging
    }
  });

  try {
    console.log('🔧 Example: Facebook Web Crawler Usage');
    console.log('=====================================');

    // Initialize the crawler
    await crawler.init();

    // Navigate to Facebook
    await crawler.navigateToFacebook();

    // Example 1: Login (uncomment and add your credentials)
    /*
    console.log('📝 Logging in...');
    await crawler.login('<EMAIL>', 'your-password');
    
    if (crawler.isLoggedIn) {
      console.log('✅ Login successful!');
      
      // Example 2: Navigate to a specific profile
      await crawler.page.goto('https://www.facebook.com/some-profile');
      await crawler.stealthUtils.sleep(2000);
      
      // Example 3: Search for something
      const searchBox = await crawler.page.$(crawler.config.facebook.selectors.searchBox);
      if (searchBox) {
        await crawler.stealthUtils.humanType(crawler.page, crawler.config.facebook.selectors.searchBox, 'your search term');
        await crawler.page.keyboard.press('Enter');
        await crawler.page.waitForNavigation();
      }
      
      // Example 4: Scroll and collect data
      for (let i = 0; i < 3; i++) {
        await crawler.stealthUtils.randomScroll(crawler.page);
        await crawler.stealthUtils.sleep(2000);
        
        // Collect posts or other data here
        const posts = await crawler.page.$$eval('[data-ad-preview="message"]', elements => 
          elements.map(el => el.textContent.trim()).filter(text => text.length > 0)
        );
        
        console.log(`Found ${posts.length} posts on scroll ${i + 1}`);
      }
    }
    */

    // Example: Just take a screenshot for testing
    await crawler.screenshot('example-screenshot.png');
    console.log('📸 Screenshot taken for testing');

    // Keep browser open for manual inspection (remove in production)
    console.log('🔍 Browser will stay open for 60 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 60000));

  } catch (error) {
    console.error('❌ Example failed:', error);
    
    // Take error screenshot for debugging
    await crawler.screenshot('error-screenshot.png');
  } finally {
    await crawler.close();
  }
}

// Advanced example with custom configuration
async function advancedExample() {
  const crawler = new FacebookCrawler({
    // Custom viewport sizes
    viewports: [
      { width: 1920, height: 1080 },
      { width: 1366, height: 768 },
    ],
    
    // Custom delays for slower, more human-like behavior
    delays: {
      pageLoad: { min: 3000, max: 6000 },
      typing: { min: 100, max: 200 },
      click: { min: 200, max: 500 },
      scroll: { min: 1000, max: 2000 },
      navigation: { min: 2000, max: 4000 },
    },
    
    // Enable proxy (add your proxy servers in config)
    proxy: {
      enabled: false, // Set to true if you have proxies configured
      rotation: true,
    },
    
    // Custom rate limiting
    rateLimit: {
      requestsPerMinute: 20, // Slower rate
      burstLimit: 3,
      cooldownPeriod: 90000, // 1.5 minutes
    }
  });

  try {
    console.log('🚀 Advanced Example: Custom Configuration');
    console.log('========================================');

    await crawler.init();
    await crawler.navigateToFacebook();
    
    // Your advanced crawling logic here
    console.log('✅ Advanced crawler ready for custom implementation');
    
    // Take screenshot
    await crawler.screenshot('advanced-example.png');
    
  } catch (error) {
    console.error('❌ Advanced example failed:', error);
  } finally {
    await crawler.close();
  }
}

// Run examples
async function runExamples() {
  console.log('Choose example to run:');
  console.log('1. Basic example');
  console.log('2. Advanced example');
  
  // For now, run basic example
  // You can modify this to accept command line arguments
  await exampleUsage();
  
  // Uncomment to run advanced example
  // await advancedExample();
}

if (require.main === module) {
  runExamples().catch(console.error);
}

module.exports = { exampleUsage, advancedExample };
