/**
 * <PERSON><PERSON> Authentication Examples for Facebook Web Crawler
 * Demonstrates various ways to use cookie-based authentication
 */

const { FacebookCrawler } = require('./index');
const username = require('dotenv').config().parsed.username;
const password = require('dotenv').config().parsed.password;
/**
 * Example 1: Basic Cookie Authentication
 * Load saved cookies and authenticate automatically
 */
async function basicCookieAuth() {
  const crawler = new FacebookCrawler();

  try {
    console.log('🍪 Example 1: Basic Cookie Authentication');
    console.log('=========================================');

    await crawler.init();

    // Check if already authenticated via cookies
    if (!crawler.isLoggedIn) {
      console.log('❌ No valid cookies found. Please login first.');
      await crawler.login(username, password);
    }

    // Take screenshot to verify
    await crawler.screenshot('cookie-auth-test.png');
    
    // Keep browser open for inspection
    await new Promise(resolve => setTimeout(resolve, 1000000));

  } catch (error) {
    console.error('<PERSON><PERSON> auth example failed:', error);
  } finally {
    await crawler.close();
  }
}

/**
 * Example 2: Manual Cookie Injection
 * Inject cookies that you copied from your browser manually
 */
async function manualCookieInjection() {
  const crawler = new FacebookCrawler({
    browserOptions: { headless: false },
    cookies: { enabled: true }
  });

  try {
    console.log('🍪 Example 2: Manual Cookie Injection');
    console.log('====================================');

    await crawler.init();

    // Example cookies array (replace with your actual cookies)
    // To get these: Open Facebook in browser -> F12 -> Application -> Cookies -> facebook.com
    // Copy the important cookies like c_user, xs, datr, sb, etc.
    const manualCookies = [
      // Example format - replace with your actual cookies
      /*
      {
        name: 'c_user',
        value: 'your_user_id',
        domain: '.facebook.com',
        path: '/',
        httpOnly: false,
        secure: true
      },
      {
        name: 'xs',
        value: 'your_xs_value',
        domain: '.facebook.com',
        path: '/',
        httpOnly: true,
        secure: true
      },
      {
        name: 'datr',
        value: 'your_datr_value',
        domain: '.facebook.com',
        path: '/',
        httpOnly: true,
        secure: true
      }
      */
    ];

    if (manualCookies.length > 0) {
      const result = await crawler.injectCookiesManually(manualCookies);
      if (result.success && result.authenticated) {
        console.log('✅ Manual cookie injection successful!');
      } else {
        console.log('❌ Manual cookie injection failed or not authenticated');
      }
    } else {
      console.log('⚠️ No manual cookies provided. Add your cookies to the manualCookies array.');
    }

    await crawler.screenshot('manual-cookie-injection.png');
    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('Manual cookie injection failed:', error);
  } finally {
    await crawler.close();
  }
}

/**
 * Example 3: Cookie Management Operations
 * Demonstrate saving, loading, listing, and managing cookies
 */
async function cookieManagement() {
  const crawler = new FacebookCrawler({
    browserOptions: { headless: false },
    cookies: { enabled: true }
  });

  try {
    console.log('🍪 Example 3: Cookie Management');
    console.log('===============================');

    await crawler.init();

    // List existing cookie files
    console.log('\n📂 Listing existing cookie files:');
    const fileList = await crawler.listCookieFiles();
    if (fileList.success && fileList.files.length > 0) {
      fileList.files.forEach(file => {
        console.log(`  - ${file.filename} (${file.cookieCount} cookies, saved: ${file.savedAt})`);
      });
    } else {
      console.log('  No cookie files found');
    }

    // Try to authenticate with existing cookies
    console.log('\n🔐 Attempting authentication with saved cookies:');
    const authResult = await crawler.authenticateWithCookies();
    
    if (authResult.success) {
      console.log('✅ Authentication successful with saved cookies');
      
      // Save cookies with a custom name
      console.log('\n💾 Saving cookies with custom name:');
      await crawler.saveCookies('backup_cookies.json');
      
      // Export cookies in different formats
      console.log('\n📤 Exporting cookies:');
      await crawler.exportCookies('json', 'exported_cookies.json');
      await crawler.exportCookies('netscape', 'exported_cookies.txt');
      
    } else {
      console.log('❌ Authentication failed - you may need to login first');
      console.log('Uncomment the login line below and add your credentials:');
      console.log('// await crawler.login("<EMAIL>", "your-password");');
    }

    await crawler.screenshot('cookie-management.png');
    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('Cookie management example failed:', error);
  } finally {
    await crawler.close();
  }
}

/**
 * Example 4: Login Once, Use Cookies Forever
 * Demonstrates the typical workflow: login once, save cookies, then use cookies
 */
async function loginOnceUseCookiesForever() {
  const crawler = new FacebookCrawler({
    browserOptions: { headless: false },
    cookies: {
      enabled: true,
      autoSave: true,
      autoLoad: true
    }
  });

  try {
    console.log('🍪 Example 4: Login Once, Use Cookies Forever');
    console.log('==============================================');

    await crawler.init();

    // Check if we already have valid cookies
    if (crawler.isLoggedIn) {
      console.log('✅ Already authenticated with saved cookies - no login needed!');
    } else {
      console.log('🔑 No valid cookies found. Need to login first.');
      console.log('Uncomment and modify the login line below:');
      console.log('// await crawler.login("<EMAIL>", "your-password");');
      
      // Uncomment this line and add your credentials for first-time setup
      // await crawler.login('<EMAIL>', 'your-password');
      
      // After successful login, cookies will be automatically saved
      if (crawler.isLoggedIn) {
        console.log('✅ Login successful! Cookies have been saved for future use.');
      }
    }

    if (crawler.isLoggedIn) {
      // Navigate to Facebook
      await crawler.navigateToFacebook();
      
      // Do your crawling work here
      console.log('🚀 Ready for crawling operations!');
      
      // Example: Get page title
      const title = await crawler.page.title();
      console.log(`Page title: ${title}`);
    }

    await crawler.screenshot('login-once-cookies-forever.png');
    await new Promise(resolve => setTimeout(resolve, 15000));

  } catch (error) {
    console.error('Login once example failed:', error);
  } finally {
    await crawler.close();
  }
}

/**
 * Helper function to display cookie copying instructions
 */
function displayCookieCopyingInstructions() {
  console.log('\n📋 How to Copy Cookies from Your Browser:');
  console.log('==========================================');
  console.log('1. Open Facebook in your browser and login');
  console.log('2. Press F12 to open Developer Tools');
  console.log('3. Go to Application tab (Chrome) or Storage tab (Firefox)');
  console.log('4. Click on Cookies -> https://www.facebook.com');
  console.log('5. Copy the important cookies (c_user, xs, datr, sb, etc.)');
  console.log('6. Format them as shown in the manualCookieInjection example');
  console.log('7. Run the manual injection example');
  console.log('\nImportant cookies to copy:');
  console.log('- c_user: Your user ID');
  console.log('- xs: Session token');
  console.log('- datr: Browser identifier');
  console.log('- sb: Session browser');
  console.log('- fr: Facebook tracking');
  console.log('- And any other cookies with long values\n');
}

// Main function to run examples
async function runCookieExamples() {
  console.log('🍪 Facebook Crawler Cookie Authentication Examples');
  console.log('==================================================\n');

  displayCookieCopyingInstructions();

  console.log('Choose which example to run:');
  console.log('1. Basic Cookie Authentication');
  console.log('2. Manual Cookie Injection');
  console.log('3. Cookie Management Operations');
  console.log('4. Login Once, Use Cookies Forever');
  console.log('\nRunning Example 1 by default...\n');

  // Run basic example by default
  // You can modify this to run different examples
  await basicCookieAuth();
  
  // Uncomment to run other examples:
  // await manualCookieInjection();
  // await cookieManagement();
  // await loginOnceUseCookiesForever();
}

if (require.main === module) {
  runCookieExamples().catch(console.error);
}

module.exports = {
  basicCookieAuth,
  manualCookieInjection,
  cookieManagement,
  loginOnceUseCookiesForever,
  displayCookieCopyingInstructions
};
