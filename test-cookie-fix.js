/**
 * Test script to verify waitForTimeout fix
 * This tests that cookie authentication works without waitForTimeout errors
 */

const { FacebookCrawler } = require('./index');

async function testCookieFix() {
  console.log('🧪 Testing Cookie Fix - waitForTimeout Issue');
  console.log('===========================================');

  const crawler = new FacebookCrawler({
    browserOptions: { 
      headless: false,
      devtools: true // Open devtools to see any errors
    },
    cookies: {
      enabled: true,
      autoLoad: true,
      validateOnLoad: true
    }
  });

  try {
    console.log('1. Initializing crawler...');
    await crawler.init();

    console.log('2. Navigating to Facebook...');
    await crawler.navigateToFacebook();

    console.log('3. Testing cookie login status check...');
    const isLoggedIn = await crawler.checkIfLoggedIn();
    console.log(`   Login status: ${isLoggedIn ? '✅ Logged in' : '❌ Not logged in'}`);

    console.log('4. Testing cookie manager directly...');
    const loginStatus = await crawler.cookieManager.checkLoginStatus(crawler.page);
    console.log(`   Cookie manager status: ${loginStatus ? '✅ Valid session' : '❌ No valid session'}`);

    console.log('5. Testing manual cookie injection (empty array)...');
    const injectResult = await crawler.injectCookiesManually([]);
    console.log(`   Injection result: ${injectResult.success ? '✅ Success' : '❌ Failed'}`);

    console.log('6. Taking screenshot for verification...');
    await crawler.screenshot('cookie-fix-test.png');

    console.log('\n✅ All tests completed without waitForTimeout errors!');
    console.log('🔍 Browser will stay open for 30 seconds for manual inspection...');
    
    // Keep browser open for inspection
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Check if it's the waitForTimeout error specifically
    if (error.message.includes('waitForTimeout') || error.message.includes('is not a function')) {
      console.error('🚨 waitForTimeout error detected! This needs to be fixed.');
    }
    
    // Take error screenshot
    try {
      await crawler.screenshot('cookie-fix-error.png');
    } catch (screenshotError) {
      console.error('Failed to take error screenshot:', screenshotError);
    }
  } finally {
    console.log('🧹 Cleaning up...');
    await crawler.close();
  }
}

async function testStealthUtilsSleep() {
  console.log('\n🧪 Testing StealthUtils Sleep Method');
  console.log('===================================');

  const crawler = new FacebookCrawler();
  
  try {
    console.log('Testing sleep method...');
    const startTime = Date.now();
    
    await crawler.stealthUtils.sleep(1000);
    
    const endTime = Date.now();
    const actualDelay = endTime - startTime;
    
    console.log(`✅ Sleep worked correctly: ${actualDelay}ms (expected ~1000ms)`);
    
    console.log('Testing randomDelay method...');
    const randomDelay = crawler.stealthUtils.randomDelay(500, 1500);
    console.log(`✅ Random delay generated: ${randomDelay}ms (range: 500-1500ms)`);
    
    console.log('Testing sleep with random delay...');
    const startTime2 = Date.now();
    await crawler.stealthUtils.sleep(randomDelay);
    const endTime2 = Date.now();
    const actualDelay2 = endTime2 - startTime2;
    
    console.log(`✅ Sleep with random delay worked: ${actualDelay2}ms (expected ~${randomDelay}ms)`);
    
  } catch (error) {
    console.error('❌ StealthUtils sleep test failed:', error);
  }
}

async function runAllTests() {
  console.log('🚀 Running Cookie Fix Tests');
  console.log('============================\n');

  // Test 1: StealthUtils sleep methods
  await testStealthUtilsSleep();

  // Test 2: Full cookie functionality
  await testCookieFix();

  console.log('\n🎉 All tests completed!');
  console.log('If no waitForTimeout errors appeared, the fix is working correctly.');
}

if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { testCookieFix, testStealthUtilsSleep };
