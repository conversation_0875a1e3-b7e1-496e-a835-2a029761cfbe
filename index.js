/**
 * Facebook Web Crawler - Main Entry Point
 * Maximum stealth configuration with anti-detection measures
 */

const FacebookCrawler = require('./src/crawler');
const username = require('dotenv').config().parsed.username;
const password = require('dotenv').config().parsed.password;

async function main() {
  const crawler = new FacebookCrawler();

  try {
    console.log('🚀 Starting Facebook Web Crawler with Maximum Stealth');
    console.log('================================================');

    // Initialize browser with stealth configuration
    await crawler.init();

    // Navigate to Facebook
    await crawler.navigateToFacebook();

    // Example: Take a screenshot for verification
    await crawler.screenshot('facebook-homepage.png');

    console.log('✅ Crawler initialized successfully!');
    console.log('📸 Screenshot saved to logs/facebook-homepage.png');
    console.log('');
    console.log('Next steps:');
    console.log('1. Use crawler.login(email, password) to login');

    await crawler.login(username,password)
    // console.log('2. Implement your crawling logic');
    // console.log('3. Use crawler.close() when done');
    // console.log('');
    // console.log('Example usage:');
    // console.log('  await crawler.login("<EMAIL>", "your-password");');
    // console.log('  // Your crawling logic here');
    // console.log('  await crawler.close();');
    //
    // // Keep the browser open for manual testing
    // // Remove this in production
    // console.log('Browser will stay open for 30 seconds for testing...');

  } catch (error) {
    console.error('❌ Crawler failed:', error);
  } finally {
    await new Promise(resolve => setTimeout(resolve, 300000));
    await crawler.close();
  }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run the crawler
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { FacebookCrawler };
