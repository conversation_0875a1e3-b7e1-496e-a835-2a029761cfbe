{"name": "WebCrawler", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js --headless=false", "debug": "node --inspect index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "private": true, "dependencies": {"dotenv": "^17.0.1", "proxy-agent": "^6.5.0", "puppeteer": "^24.11.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-anonymize-ua": "^2.4.6", "puppeteer-extra-plugin-recaptcha": "^3.6.8", "puppeteer-extra-plugin-stealth": "^2.11.2", "user-agents": "^1.1.592"}}