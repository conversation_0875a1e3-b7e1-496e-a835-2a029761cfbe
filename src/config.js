/**
 * Configuration file for Facebook Web Crawler
 * Maximum stealth settings and anti-detection measures
 */

module.exports = {
  // Browser launch options for maximum stealth
  browserOptions: {
    headless: false, // Use new headless mode
    args: [
      // Basic stealth args
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--single-process',
      '--disable-gpu',
      
      // Anti-detection args
      '--disable-blink-features=AutomationControlled',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-networking',
      '--disable-background-timer-throttling',
      '--disable-renderer-backgrounding',
      '--disable-backgrounding-occluded-windows',
      '--disable-client-side-phishing-detection',
      '--disable-component-extensions-with-background-pages',
      '--disable-default-apps',
      '--disable-extensions',
      '--disable-features=TranslateUI',
      '--disable-hang-monitor',
      '--disable-ipc-flooding-protection',
      '--disable-popup-blocking',
      '--disable-prompt-on-repost',
      '--disable-sync',
      '--disable-web-security',
      '--metrics-recording-only',
      '--no-default-browser-check',
      '--no-pings',
      '--password-store=basic',
      '--use-mock-keychain',
      
      // WebRTC leak prevention
      '--disable-webrtc',
      '--disable-webrtc-multiple-routes',
      '--disable-webrtc-hw-decoding',
      '--disable-webrtc-hw-encoding',
      '--enforce-webrtc-ip-permission-check',
      '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',
      
      // Additional fingerprinting protection
      '--disable-canvas-aa',
      '--disable-2d-canvas-clip-aa',
      '--disable-gl-drawing-for-tests',
      '--disable-webgl',
      '--disable-webgl2',
      '--disable-plugins-discovery',
      '--disable-bundled-ppapi-flash',
      
      // Memory and performance
      '--memory-pressure-off',
      '--max_old_space_size=4096',
    ],
    ignoreHTTPSErrors: true,
    ignoreDefaultArgs: ['--enable-automation'],
  },

  // Viewport configurations for randomization
  viewports: [
    { width: 1366, height: 768 },
    { width: 1920, height: 1080 },
    { width: 1440, height: 900 },
    { width: 1536, height: 864 },
    { width: 1280, height: 720 },
    { width: 1600, height: 900 },
    { width: 1024, height: 768 },
  ],

  // Timing configurations for human-like behavior
  delays: {
    pageLoad: { min: 2000, max: 5000 },
    typing: { min: 50, max: 150 },
    click: { min: 100, max: 300 },
    scroll: { min: 500, max: 1500 },
    navigation: { min: 1000, max: 3000 },
  },

  // Facebook specific selectors and URLs
  facebook: {
    baseUrl: 'https://www.facebook.com',
    loginUrl: 'https://www.facebook.com/login',
    selectors: {
      emailInput: '#email',
      passwordInput: '#pass',
      loginButton: '[name="login"]',
      searchBox: '[placeholder*="Search"]',
      postContent: '[data-ad-preview="message"]',
      profileLink: '[data-testid="nav-header-PROFILE"]',
    },
  },

  // Stealth plugin configurations
  stealth: {
    // Chrome runtime stealth
    chromeRuntime: {
      enabled: true,
    },
    // Navigator stealth
    navigator: {
      enabled: true,
      // Override navigator properties
      userAgent: null, // Will be set dynamically
      languages: ['en-US', 'en'],
      platform: 'Win32',
      hardwareConcurrency: 4,
    },
    // WebGL stealth
    webgl: {
      enabled: true,
      // Randomize WebGL fingerprint
      vendor: 'Intel Inc.',
      renderer: 'Intel(R) HD Graphics',
    },
    // Media codecs stealth
    mediaCodecs: {
      enabled: true,
    },
    // Permissions stealth
    permissions: {
      enabled: true,
    },
  },

  // Proxy configuration (optional)
  proxy: {
    enabled: false,
    servers: [
      // Add your proxy servers here
      // { host: 'proxy1.example.com', port: 8080, username: 'user', password: 'pass' }
    ],
    rotation: true, // Rotate proxies for each session
  },

  // Logging configuration
  logging: {
    enabled: true,
    level: 'info', // debug, info, warn, error
    file: './logs/crawler.log',
    console: true,
  },

  // Rate limiting to avoid detection
  rateLimit: {
    requestsPerMinute: 30,
    burstLimit: 5,
    cooldownPeriod: 60000, // 1 minute
  },

  // User agent rotation
  userAgents: {
    rotate: true,
    // Will be populated with realistic user agents
    pool: [],
  },

  // Cookie management configuration
  cookies: {
    enabled: true,
    directory: './cookies', // Directory to store cookie files
    defaultFile: 'facebook_cookies.json', // Default cookie filename
    autoSave: true, // Automatically save cookies after successful login
    autoLoad: true, // Automatically try to load cookies on init
    validateOnLoad: true, // Validate cookies when loading
    backupOnSave: true, // Create backup of existing cookies
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    compression: false, // Compress cookie files (future feature)
  },
};
