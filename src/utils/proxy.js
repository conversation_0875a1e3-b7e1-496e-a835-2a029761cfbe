/**
 * Proxy management utilities for IP rotation and anonymity
 */

class ProxyManager {
  constructor(config) {
    this.config = config;
    this.proxies = config.proxy.servers || [];
    this.currentProxyIndex = 0;
    this.failedProxies = new Set();
  }

  /**
   * Get next proxy in rotation
   */
  getNextProxy() {
    if (!this.config.proxy.enabled || this.proxies.length === 0) {
      return null;
    }

    // Filter out failed proxies
    const availableProxies = this.proxies.filter((_, index) => 
      !this.failedProxies.has(index)
    );

    if (availableProxies.length === 0) {
      console.warn('All proxies have failed, clearing failed list');
      this.failedProxies.clear();
      return this.proxies[0];
    }

    if (this.config.proxy.rotation) {
      this.currentProxyIndex = (this.currentProxyIndex + 1) % availableProxies.length;
    }

    return availableProxies[this.currentProxyIndex];
  }

  /**
   * Mark proxy as failed
   */
  markProxyAsFailed(proxy) {
    const index = this.proxies.findIndex(p => 
      p.host === proxy.host && p.port === proxy.port
    );
    if (index !== -1) {
      this.failedProxies.add(index);
      console.warn(`Marked proxy as failed: ${proxy.host}:${proxy.port}`);
    }
  }

  /**
   * Get proxy URL for browser launch
   */
  getProxyUrl(proxy) {
    if (!proxy) return null;

    const auth = proxy.username && proxy.password 
      ? `${proxy.username}:${proxy.password}@` 
      : '';
    
    return `http://${auth}${proxy.host}:${proxy.port}`;
  }

  /**
   * Test proxy connectivity
   */
  async testProxy(proxy) {
    try {
      const proxyUrl = this.getProxyUrl(proxy);
      console.log(`Testing proxy: ${proxy.host}:${proxy.port}`);
      
      // You can implement actual proxy testing here
      // For now, we'll assume it's working
      return true;
    } catch (error) {
      console.error(`Proxy test failed for ${proxy.host}:${proxy.port}:`, error.message);
      return false;
    }
  }

  /**
   * Get browser args with proxy configuration
   */
  getProxyArgs() {
    const proxy = this.getNextProxy();
    if (!proxy) return [];

    const proxyUrl = this.getProxyUrl(proxy);
    console.log(`Using proxy: ${proxy.host}:${proxy.port}`);

    return [
      `--proxy-server=${proxyUrl}`,
      '--proxy-bypass-list=<-loopback>',
    ];
  }

  /**
   * Reset proxy rotation
   */
  reset() {
    this.currentProxyIndex = 0;
    this.failedProxies.clear();
  }

  /**
   * Get proxy statistics
   */
  getStats() {
    return {
      total: this.proxies.length,
      failed: this.failedProxies.size,
      available: this.proxies.length - this.failedProxies.size,
      current: this.currentProxyIndex,
    };
  }
}

module.exports = ProxyManager;
