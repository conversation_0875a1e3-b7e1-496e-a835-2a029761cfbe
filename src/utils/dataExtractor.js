/**
 * Data Extraction Utility for Facebook Posts
 * Extracts comprehensive data from posts, comments, reactions, and shares
 */

class DataExtractor {
  constructor(stealthUtils = null) {
    this.stealthUtils = stealthUtils;
  }

  /**
   * Extract comprehensive post data
   */
  async extractPostData(page, postElement) {
    try {
      const postData = await page.evaluate((element) => {
        const post = {
          id: null,
          content: '',
          author: {
            name: '',
            profileUrl: '',
            avatarUrl: '',
            verified: false
          },
          timestamp: '',
          engagement: {
            likes: 0,
            comments: 0,
            shares: 0,
            reactions: {
              like: 0,
              love: 0,
              haha: 0,
              wow: 0,
              sad: 0,
              angry: 0
            }
          },
          media: {
            images: [],
            videos: [],
            links: []
          },
          hashtags: [],
          mentions: [],
          location: '',
          postType: 'text', // text, photo, video, link, shared
          url: '',
          privacy: 'public'
        };

        // Extract post ID from data attributes
        const postId = element.getAttribute('data-ft') || 
                      element.getAttribute('data-testid') ||
                      element.querySelector('[data-ft]')?.getAttribute('data-ft');
        if (postId) {
          try {
            const ftData = JSON.parse(postId);
            post.id = ftData.mf_story_key || ftData.top_level_post_id;
          } catch (e) {
            post.id = postId;
          }
        }

        // Extract author information
        const authorLink = element.querySelector('a[role="link"][tabindex="0"]') ||
                          element.querySelector('h3 a') ||
                          element.querySelector('[data-hovercard-prefer-more-content-show="1"]');
        
        if (authorLink) {
          post.author.name = authorLink.textContent?.trim() || '';
          post.author.profileUrl = authorLink.href || '';
          
          // Check for verification badge
          const verificationBadge = authorLink.parentElement?.querySelector('[aria-label*="Verified"]') ||
                                   authorLink.parentElement?.querySelector('.x1lliihq.x1plvlek.xryxfnj.x1n2onr6.x193iq5w.xeuugli.x1fj9vlw.x13faqbe.x1vvkbs.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.x1i0vuye.xvs91rp.x1s688f.x5n08af.x10wh9bi.x1wdrske.x8viiok.x18hxmgj');
          post.author.verified = !!verificationBadge;
        }

        // Extract avatar
        const avatar = element.querySelector('image') || 
                      element.querySelector('img[data-imgperflogname="profileCoverPhoto"]') ||
                      element.querySelector('img[alt*="profile picture"]');
        if (avatar) {
          post.author.avatarUrl = avatar.src || avatar.getAttribute('xlink:href') || '';
        }

        // Extract post content
        const contentElement = element.querySelector('[data-ad-preview="message"]') ||
                              element.querySelector('[data-testid="post_message"]') ||
                              element.querySelector('.userContent') ||
                              element.querySelector('.x11i5rnm.xat24cr.x1mh8g0r.x1vvkbs');
        
        if (contentElement) {
          post.content = contentElement.textContent?.trim() || '';
          
          // Extract hashtags
          const hashtagRegex = /#[\w\u00c0-\u024f\u1e00-\u1eff]+/g;
          post.hashtags = (post.content.match(hashtagRegex) || []).map(tag => tag.toLowerCase());
          
          // Extract mentions
          const mentionRegex = /@[\w\u00c0-\u024f\u1e00-\u1eff]+/g;
          post.mentions = (post.content.match(mentionRegex) || []).map(mention => mention.toLowerCase());
        }

        // Extract timestamp
        const timeElement = element.querySelector('a[role="link"] abbr') ||
                           element.querySelector('abbr[data-utime]') ||
                           element.querySelector('time');
        if (timeElement) {
          post.timestamp = timeElement.getAttribute('data-utime') ||
                          timeElement.getAttribute('title') ||
                          timeElement.textContent?.trim() || '';
        }

        // Extract engagement metrics
        const reactionsElement = element.querySelector('[aria-label*="reaction"]') ||
                                element.querySelector('[data-testid="UFI2ReactionsCount/root"]');
        if (reactionsElement) {
          const reactionsText = reactionsElement.textContent || reactionsElement.getAttribute('aria-label') || '';
          const likesMatch = reactionsText.match(/(\d+(?:,\d+)*)\s*(?:like|reaction)/i);
          if (likesMatch) {
            post.engagement.likes = parseInt(likesMatch[1].replace(/,/g, '')) || 0;
          }
        }

        // Extract comments count
        const commentsElement = element.querySelector('[aria-label*="comment"]') ||
                               element.querySelector('[data-testid="UFI2CommentsCount/root"]');
        if (commentsElement) {
          const commentsText = commentsElement.textContent || commentsElement.getAttribute('aria-label') || '';
          const commentsMatch = commentsText.match(/(\d+(?:,\d+)*)\s*comment/i);
          if (commentsMatch) {
            post.engagement.comments = parseInt(commentsMatch[1].replace(/,/g, '')) || 0;
          }
        }

        // Extract shares count
        const sharesElement = element.querySelector('[aria-label*="share"]') ||
                             element.querySelector('[data-testid="UFI2SharesCount/root"]');
        if (sharesElement) {
          const sharesText = sharesElement.textContent || sharesElement.getAttribute('aria-label') || '';
          const sharesMatch = sharesText.match(/(\d+(?:,\d+)*)\s*share/i);
          if (sharesMatch) {
            post.engagement.shares = parseInt(sharesMatch[1].replace(/,/g, '')) || 0;
          }
        }

        // Extract media
        const images = Array.from(element.querySelectorAll('img[src*="scontent"]')).map(img => ({
          url: img.src,
          alt: img.alt || ''
        }));
        post.media.images = images;

        const videos = Array.from(element.querySelectorAll('video')).map(video => ({
          url: video.src || video.querySelector('source')?.src || '',
          poster: video.poster || ''
        }));
        post.media.videos = videos;

        // Determine post type
        if (post.media.images.length > 0) post.postType = 'photo';
        else if (post.media.videos.length > 0) post.postType = 'video';
        else if (element.querySelector('[data-testid="post-link"]')) post.postType = 'link';
        else if (element.querySelector('[data-testid="post-shared"]')) post.postType = 'shared';

        // Extract post URL
        const postLink = element.querySelector('a[href*="/posts/"]') ||
                        element.querySelector('a[href*="/permalink/"]') ||
                        element.querySelector('a[href*="/story.php"]');
        if (postLink) {
          post.url = postLink.href;
        }

        return post;
      }, postElement);

      return postData;
    } catch (error) {
      console.error('Error extracting post data:', error);
      return null;
    }
  }

  /**
   * Extract comments from a post
   */
  async extractComments(page, postElement, maxComments = 50) {
    try {
      const comments = [];
      
      // Click "View more comments" if available
      await this.expandComments(page, postElement);

      const commentElements = await postElement.$$('[data-testid="UFI2Comment/root"]') ||
                             await postElement.$$('.UFIComment') ||
                             await postElement.$$('[role="article"]');

      for (let i = 0; i < Math.min(commentElements.length, maxComments); i++) {
        const commentElement = commentElements[i];
        
        const commentData = await page.evaluate((element) => {
          const comment = {
            id: '',
            author: {
              name: '',
              profileUrl: '',
              avatarUrl: '',
              verified: false
            },
            content: '',
            timestamp: '',
            likes: 0,
            replies: [],
            isReply: false
          };

          // Extract author info
          const authorLink = element.querySelector('a[role="link"]');
          if (authorLink) {
            comment.author.name = authorLink.textContent?.trim() || '';
            comment.author.profileUrl = authorLink.href || '';
          }

          // Extract avatar
          const avatar = element.querySelector('img');
          if (avatar) {
            comment.author.avatarUrl = avatar.src || '';
          }

          // Extract content
          const contentElement = element.querySelector('[dir="auto"]') ||
                                element.querySelector('.UFICommentBody');
          if (contentElement) {
            comment.content = contentElement.textContent?.trim() || '';
          }

          // Extract timestamp
          const timeElement = element.querySelector('abbr') ||
                             element.querySelector('time');
          if (timeElement) {
            comment.timestamp = timeElement.getAttribute('data-utime') ||
                               timeElement.getAttribute('title') ||
                               timeElement.textContent?.trim() || '';
          }

          // Extract likes
          const likeElement = element.querySelector('[aria-label*="like"]');
          if (likeElement) {
            const likesText = likeElement.textContent || '';
            const likesMatch = likesText.match(/(\d+)/);
            if (likesMatch) {
              comment.likes = parseInt(likesMatch[1]) || 0;
            }
          }

          return comment;
        }, commentElement);

        if (commentData && commentData.content) {
          comments.push(commentData);
        }
      }

      return comments;
    } catch (error) {
      console.error('Error extracting comments:', error);
      return [];
    }
  }

  /**
   * Expand comments section to load more comments
   */
  async expandComments(page, postElement) {
    try {
      // Look for "View more comments" or similar buttons
      const expandButtons = [
        'View more comments',
        'View previous comments',
        'Show more comments',
        'Load more comments'
      ];

      for (const buttonText of expandButtons) {
        try {
          const button = await postElement.$(`[aria-label*="${buttonText}"]`) ||
                        await postElement.$(`text=${buttonText}`) ||
                        await postElement.$(`[role="button"]:has-text("${buttonText}")`);
          
          if (button) {
            await button.click();
            if (this.stealthUtils) {
              await this.stealthUtils.sleep(this.stealthUtils.randomDelay(1000, 2000));
            } else {
              await new Promise(resolve => setTimeout(resolve, 1500));
            }
            break;
          }
        } catch (e) {
          // Continue to next button type
        }
      }
    } catch (error) {
      console.log('Could not expand comments:', error.message);
    }
  }

  /**
   * Extract detailed reaction information
   */
  async extractReactions(page, postElement) {
    try {
      const reactions = {
        total: 0,
        breakdown: {
          like: 0,
          love: 0,
          haha: 0,
          wow: 0,
          sad: 0,
          angry: 0,
          care: 0
        },
        topReactors: []
      };

      // Click on reactions to open detailed view
      const reactionsButton = await postElement.$('[data-testid="UFI2ReactionsCount/root"]') ||
                             await postElement.$('[aria-label*="reaction"]');
      
      if (reactionsButton) {
        await reactionsButton.click();
        
        if (this.stealthUtils) {
          await this.stealthUtils.sleep(this.stealthUtils.randomDelay(2000, 3000));
        } else {
          await new Promise(resolve => setTimeout(resolve, 2500));
        }

        // Extract reaction details from modal
        const reactionData = await page.evaluate(() => {
          const modal = document.querySelector('[role="dialog"]') ||
                       document.querySelector('.uiLayer');
          
          if (!modal) return null;

          const data = {
            total: 0,
            breakdown: {},
            topReactors: []
          };

          // Extract reaction counts from tabs
          const reactionTabs = modal.querySelectorAll('[role="tab"]');
          reactionTabs.forEach(tab => {
            const text = tab.textContent || '';
            const countMatch = text.match(/(\d+)/);
            if (countMatch) {
              const count = parseInt(countMatch[1]);
              if (text.includes('All')) data.total = count;
              else if (text.includes('👍') || text.includes('Like')) data.breakdown.like = count;
              else if (text.includes('❤️') || text.includes('Love')) data.breakdown.love = count;
              else if (text.includes('😂') || text.includes('Haha')) data.breakdown.haha = count;
              else if (text.includes('😮') || text.includes('Wow')) data.breakdown.wow = count;
              else if (text.includes('😢') || text.includes('Sad')) data.breakdown.sad = count;
              else if (text.includes('😡') || text.includes('Angry')) data.breakdown.angry = count;
              else if (text.includes('🤗') || text.includes('Care')) data.breakdown.care = count;
            }
          });

          // Extract top reactors
          const reactorElements = modal.querySelectorAll('[data-testid="reaction_profile_browser_list_item"]');
          reactorElements.forEach((element, index) => {
            if (index < 20) { // Limit to top 20
              const nameElement = element.querySelector('a[role="link"]');
              const avatarElement = element.querySelector('img');
              
              if (nameElement) {
                data.topReactors.push({
                  name: nameElement.textContent?.trim() || '',
                  profileUrl: nameElement.href || '',
                  avatarUrl: avatarElement?.src || ''
                });
              }
            }
          });

          return data;
        });

        if (reactionData) {
          reactions.total = reactionData.total;
          reactions.breakdown = { ...reactions.breakdown, ...reactionData.breakdown };
          reactions.topReactors = reactionData.topReactors;
        }

        // Close modal
        const closeButton = await page.$('[aria-label="Close"]') ||
                           await page.$('[data-testid="modal-close-button"]');
        if (closeButton) {
          await closeButton.click();
          if (this.stealthUtils) {
            await this.stealthUtils.sleep(this.stealthUtils.randomDelay(500, 1000));
          }
        }
      }

      return reactions;
    } catch (error) {
      console.error('Error extracting reactions:', error);
      return {
        total: 0,
        breakdown: {},
        topReactors: []
      };
    }
  }

  /**
   * Extract share information
   */
  async extractShares(page, postElement) {
    try {
      const shares = {
        count: 0,
        topSharers: []
      };

      // Click on shares to open detailed view
      const sharesButton = await postElement.$('[data-testid="UFI2SharesCount/root"]') ||
                          await postElement.$('[aria-label*="share"]');
      
      if (sharesButton) {
        await sharesButton.click();
        
        if (this.stealthUtils) {
          await this.stealthUtils.sleep(this.stealthUtils.randomDelay(2000, 3000));
        }

        // Extract share details (implementation depends on Facebook's current structure)
        // This is a placeholder for share extraction logic
        
        // Close modal if opened
        const closeButton = await page.$('[aria-label="Close"]');
        if (closeButton) {
          await closeButton.click();
        }
      }

      return shares;
    } catch (error) {
      console.error('Error extracting shares:', error);
      return { count: 0, topSharers: [] };
    }
  }
}

module.exports = DataExtractor;
