/**
 * Advanced stealth utilities for maximum anti-detection
 */

const UserAgent = require('user-agents');

class StealthUtils {
  constructor() {
    this.userAgentGenerator = new UserAgent({ deviceCategory: 'desktop' });
  }

  /**
   * Get a random realistic user agent
   */
  getRandomUserAgent() {
    return this.userAgentGenerator.random().toString();
  }

  /**
   * Get random viewport dimensions
   */
  getRandomViewport(viewports) {
    return viewports[Math.floor(Math.random() * viewports.length)];
  }

  /**
   * Generate random delay within range
   */
  randomDelay(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Sleep for specified milliseconds
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Human-like typing simulation
   */
  async humanType(page, selector, text, options = {}) {
    const element = await page.$(selector);
    if (!element) {
      throw new Error(`Element not found: ${selector}`);
    }

    await element.click();
    await this.sleep(this.randomDelay(100, 300));

    // Clear existing text
    await element.evaluate(el => el.value = '');

    // Type character by character with random delays
    for (const char of text) {
      await element.type(char, { 
        delay: this.randomDelay(options.minDelay || 50, options.maxDelay || 150) 
      });
    }
  }

  /**
   * Human-like mouse movement and click
   */
  async humanClick(page, selector, options = {}) {
    const element = await page.$(selector);
    if (!element) {
      throw new Error(`Element not found: ${selector}`);
    }

    // Get element position
    const box = await element.boundingBox();
    if (!box) {
      throw new Error(`Element not visible: ${selector}`);
    }

    // Random position within element
    const x = box.x + Math.random() * box.width;
    const y = box.y + Math.random() * box.height;

    // Move mouse to position with curve
    await page.mouse.move(x, y, { steps: this.randomDelay(5, 15) });
    await this.sleep(this.randomDelay(100, 300));

    // Click with random delay
    await page.mouse.click(x, y, { 
      delay: this.randomDelay(options.minDelay || 50, options.maxDelay || 150) 
    });
  }

  /**
   * Random scroll simulation
   */
  async randomScroll(page, options = {}) {
    const scrollDistance = this.randomDelay(options.minDistance || 200, options.maxDistance || 800);
    const scrollSteps = this.randomDelay(3, 8);
    const stepDistance = scrollDistance / scrollSteps;

    for (let i = 0; i < scrollSteps; i++) {
      await page.evaluate((distance) => {
        window.scrollBy(0, distance);
      }, stepDistance);
      await this.sleep(this.randomDelay(100, 300));
    }
  }

  /**
   * Override navigator properties to avoid detection
   */
  async overrideNavigatorProperties(page) {
    await page.evaluateOnNewDocument(() => {
      // Override webdriver property
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // Override plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // Override languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });

      // Override permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );

      // Override chrome runtime
      if (!window.chrome) {
        window.chrome = {};
      }
      if (!window.chrome.runtime) {
        window.chrome.runtime = {};
      }
    });
  }

  /**
   * Hide WebRTC IP leaks
   */
  async hideWebRTC(page) {
    await page.evaluateOnNewDocument(() => {
      // Disable WebRTC
      const getOrig = RTCPeerConnection.prototype.createDataChannel;
      RTCPeerConnection.prototype.createDataChannel = function(...args) {
        return getOrig.apply(this, args);
      };

      // Override getUserMedia
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
      }

      // Block WebRTC IP enumeration
      const noop = () => {};
      
      window.RTCPeerConnection = window.RTCPeerConnection || noop;
      window.RTCSessionDescription = window.RTCSessionDescription || noop;
      window.RTCIceCandidate = window.RTCIceCandidate || noop;
      window.webkitRTCPeerConnection = window.webkitRTCPeerConnection || noop;
      window.webkitRTCSessionDescription = window.webkitRTCSessionDescription || noop;
      window.webkitRTCIceCandidate = window.webkitRTCIceCandidate || noop;
    });
  }

  /**
   * Apply all stealth measures to a page
   */
  async applyStealthMeasures(page, config) {
    await this.overrideNavigatorProperties(page);
    await this.hideWebRTC(page);

    // Set random viewport
    const viewport = this.getRandomViewport(config.viewports);
    await page.setViewport(viewport);

    // Set random user agent
    const userAgent = this.getRandomUserAgent();
    await page.setUserAgent(userAgent);

    // Set additional headers
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Upgrade-Insecure-Requests': '1',
      'Cache-Control': 'max-age=0',
    });

    console.log(`Applied stealth measures - Viewport: ${viewport.width}x${viewport.height}, UA: ${userAgent.substring(0, 50)}...`);
  }
}

module.exports = StealthUtils;
