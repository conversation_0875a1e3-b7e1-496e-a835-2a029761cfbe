/**
 * Cookie management utilities for persistent authentication
 * Handles saving, loading, and injecting cookies for Facebook sessions
 */

const fs = require('fs').promises;
const path = require('path');

class CookieManager {
  constructor(config) {
    this.config = config;
    this.cookiesDir = config.cookies?.directory || './cookies';
    this.defaultCookieFile = config.cookies?.defaultFile || 'facebook_cookies.json';
    this.ensureCookiesDirectory();
  }

  /**
   * Ensure cookies directory exists
   */
  async ensureCookiesDirectory() {
    try {
      await fs.mkdir(this.cookiesDir, { recursive: true });
    } catch (error) {
      console.warn('Failed to create cookies directory:', error.message);
    }
  }

  /**
   * Save cookies from current page session
   */
  async saveCookies(page, filename = null) {
    try {
      const cookieFile = filename || this.defaultCookieFile;
      const cookiePath = path.join(this.cookiesDir, cookieFile);

      // Get all cookies from the current page
      const cookies = await page.cookies();
      
      // Filter Facebook-related cookies for security
      const facebookCookies = cookies.filter(cookie => 
        cookie.domain.includes('facebook.com') || 
        cookie.domain.includes('.facebook.com')
      );

      // Add metadata
      const cookieData = {
        cookies: facebookCookies,
        savedAt: new Date().toISOString(),
        domain: 'facebook.com',
        userAgent: await page.evaluate(() => navigator.userAgent),
        url: page.url(),
        count: facebookCookies.length
      };

      // Save to file
      await fs.writeFile(cookiePath, JSON.stringify(cookieData, null, 2));
      
      console.log(`✅ Saved ${facebookCookies.length} cookies to ${cookiePath}`);
      return {
        success: true,
        path: cookiePath,
        count: facebookCookies.length,
        cookies: facebookCookies
      };
    } catch (error) {
      console.error('❌ Failed to save cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Load cookies from file
   */
  async loadCookies(filename = null) {
    try {
      const cookieFile = filename || this.defaultCookieFile;
      const cookiePath = path.join(this.cookiesDir, cookieFile);

      // Check if file exists
      try {
        await fs.access(cookiePath);
      } catch {
        console.warn(`Cookie file not found: ${cookiePath}`);
        return { success: false, error: 'Cookie file not found' };
      }

      // Read and parse cookie file
      const cookieData = JSON.parse(await fs.readFile(cookiePath, 'utf8'));
      
      // Validate cookie data structure
      if (!cookieData.cookies || !Array.isArray(cookieData.cookies)) {
        throw new Error('Invalid cookie file format');
      }

      console.log(`📂 Loaded ${cookieData.cookies.length} cookies from ${cookiePath}`);
      console.log(`   Saved at: ${cookieData.savedAt}`);
      
      return {
        success: true,
        data: cookieData,
        cookies: cookieData.cookies,
        count: cookieData.cookies.length
      };
    } catch (error) {
      console.error('❌ Failed to load cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Inject cookies into page
   */
  async injectCookies(page, cookies) {
    try {
      if (!cookies || !Array.isArray(cookies)) {
        throw new Error('Invalid cookies data');
      }

      // Navigate to Facebook first to set the domain context
      await page.goto('https://www.facebook.com', { waitUntil: 'domcontentloaded' });

      // Set each cookie
      for (const cookie of cookies) {
        try {
          // Ensure cookie has required properties
          const cookieToSet = {
            name: cookie.name,
            value: cookie.value,
            domain: cookie.domain,
            path: cookie.path || '/',
            httpOnly: cookie.httpOnly || false,
            secure: cookie.secure || false,
            sameSite: cookie.sameSite || 'Lax'
          };

          // Remove expiration for session cookies or set if provided
          if (cookie.expires && cookie.expires > 0) {
            cookieToSet.expires = cookie.expires;
          }

          await page.setCookie(cookieToSet);
        } catch (cookieError) {
          console.warn(`Failed to set cookie ${cookie.name}:`, cookieError.message);
        }
      }

      console.log(`✅ Injected ${cookies.length} cookies into page`);
      return { success: true, count: cookies.length };
    } catch (error) {
      console.error('❌ Failed to inject cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Load and inject cookies from file
   */
  async loadAndInjectCookies(page, filename = null) {
    try {
      const loadResult = await this.loadCookies(filename);
      if (!loadResult.success) {
        return loadResult;
      }

      const injectResult = await this.injectCookies(page, loadResult.cookies);
      if (!injectResult.success) {
        return injectResult;
      }

      return {
        success: true,
        loaded: loadResult.count,
        injected: injectResult.count,
        message: `Successfully loaded and injected ${loadResult.count} cookies`
      };
    } catch (error) {
      console.error('❌ Failed to load and inject cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate if cookies are still valid
   */
  async validateCookies(page, cookies) {
    try {
      // Inject cookies
      const injectResult = await this.injectCookies(page, cookies);
      if (!injectResult.success) {
        return { valid: false, reason: 'Failed to inject cookies' };
      }

      // Navigate to Facebook and check if logged in
      await page.goto('https://www.facebook.com', { waitUntil: 'networkidle2' });
      
      // Check for login indicators
      const isLoggedIn = await this.checkLoginStatus(page);
      
      return {
        valid: isLoggedIn,
        reason: isLoggedIn ? 'Cookies are valid' : 'Cookies expired or invalid'
      };
    } catch (error) {
      return { valid: false, reason: error.message };
    }
  }

  /**
   * Check if user is logged in based on page content
   */
  async checkLoginStatus(page) {
    try {
      // Wait a bit for page to load
      await page.waitForNavigation({ waitUntil: 'networkidle2' });

      // Check for various login indicators
      const loginIndicators = [
        '[data-testid="nav-header-PROFILE"]', // Profile link
        '[aria-label="Account"]', // Account menu
        '[data-testid="blue_bar_profile_link"]', // Profile link (alternative)
        'div[role="banner"] a[href*="/me"]', // Profile link in banner
      ];

      for (const selector of loginIndicators) {
        try {
          const element = await page.$(selector);
          if (element) {
            console.log(`✅ Login detected via selector: ${selector}`);
            return true;
          }
        } catch (e) {
          // Continue checking other selectors
        }
      }

      // Check URL for login page
      const currentUrl = page.url();
      if (currentUrl.includes('/login') || currentUrl.includes('/checkpoint')) {
        console.log('❌ Redirected to login/checkpoint page');
        return false;
      }

      // Check page title
      const title = await page.title();
      if (title.includes('Log in') || title.includes('Sign up')) {
        console.log('❌ Page title indicates not logged in');
        return false;
      }

      console.log('⚠️ Login status unclear, assuming not logged in');
      return false;
    } catch (error) {
      console.error('Error checking login status:', error);
      return false;
    }
  }

  /**
   * List available cookie files
   */
  async listCookieFiles() {
    try {
      const files = await fs.readdir(this.cookiesDir);
      const cookieFiles = files.filter(file => file.endsWith('.json'));
      
      const fileDetails = [];
      for (const file of cookieFiles) {
        try {
          const filePath = path.join(this.cookiesDir, file);
          const stats = await fs.stat(filePath);
          const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
          
          fileDetails.push({
            filename: file,
            path: filePath,
            size: stats.size,
            modified: stats.mtime,
            cookieCount: data.cookies?.length || 0,
            savedAt: data.savedAt,
            domain: data.domain
          });
        } catch (e) {
          // Skip invalid files
        }
      }

      return { success: true, files: fileDetails };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete cookie file
   */
  async deleteCookieFile(filename) {
    try {
      const cookiePath = path.join(this.cookiesDir, filename);
      await fs.unlink(cookiePath);
      console.log(`🗑️ Deleted cookie file: ${filename}`);
      return { success: true };
    } catch (error) {
      console.error(`❌ Failed to delete cookie file ${filename}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Export cookies in different formats
   */
  async exportCookies(page, format = 'json', filename = null) {
    try {
      const cookies = await page.cookies();
      const facebookCookies = cookies.filter(cookie => 
        cookie.domain.includes('facebook.com')
      );

      const exportFile = filename || `facebook_cookies_export_${Date.now()}.${format}`;
      const exportPath = path.join(this.cookiesDir, exportFile);

      let content;
      switch (format.toLowerCase()) {
        case 'json':
          content = JSON.stringify(facebookCookies, null, 2);
          break;
        case 'netscape':
          content = this.convertToNetscapeFormat(facebookCookies);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      await fs.writeFile(exportPath, content);
      console.log(`📤 Exported ${facebookCookies.length} cookies to ${exportPath}`);
      
      return { success: true, path: exportPath, count: facebookCookies.length };
    } catch (error) {
      console.error('❌ Failed to export cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Convert cookies to Netscape format
   */
  convertToNetscapeFormat(cookies) {
    let content = '# Netscape HTTP Cookie File\n';
    content += '# This is a generated file! Do not edit.\n\n';

    for (const cookie of cookies) {
      const domain = cookie.domain.startsWith('.') ? cookie.domain : `.${cookie.domain}`;
      const flag = 'TRUE';
      const path = cookie.path || '/';
      const secure = cookie.secure ? 'TRUE' : 'FALSE';
      const expires = cookie.expires ? Math.floor(cookie.expires) : '0';
      const name = cookie.name;
      const value = cookie.value;

      content += `${domain}\t${flag}\t${path}\t${secure}\t${expires}\t${name}\t${value}\n`;
    }

    return content;
  }
}

module.exports = CookieManager;
