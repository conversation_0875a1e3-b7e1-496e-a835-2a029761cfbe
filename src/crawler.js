/**
 * Facebook Web Crawler with Maximum Stealth
 * Advanced anti-detection and human-like behavior simulation
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const AdblockerPlugin = require('puppeteer-extra-plugin-adblocker');
const RecaptchaPlugin = require('puppeteer-extra-plugin-recaptcha');
const AnonymizeUAPlugin = require('puppeteer-extra-plugin-anonymize-ua');

const config = require('./config');
const StealthUtils = require('./utils/stealth');
const ProxyManager = require('./utils/proxy');
const CookieManager = require('./utils/cookies');

// Configure plugins
puppeteer.use(StealthPlugin());
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));
puppeteer.use(AnonymizeUAPlugin());

// Optional: Configure reCAPTCHA plugin (requires 2captcha API key)
// puppeteer.use(RecaptchaPlugin({
//   provider: { id: '2captcha', token: 'YOUR_2CAPTCHA_API_KEY' },
//   visualFeedback: true
// }));

class FacebookCrawler {
  constructor(options = {}) {
    this.config = { ...config, ...options };
    this.browser = null;
    this.page = null;
    this.stealthUtils = new StealthUtils();
    this.proxyManager = new ProxyManager(this.config);
    this.cookieManager = new CookieManager(this.config);
    this.isLoggedIn = false;
    this.requestCount = 0;
    this.lastRequestTime = Date.now();
  }

  /**
   * Initialize browser with maximum stealth configuration
   */
  async init() {
    try {
      console.log('Initializing Facebook crawler with maximum stealth...');

      // Get proxy configuration
      const proxyArgs = this.proxyManager.getProxyArgs();
      
      // Merge browser options with proxy args
      const browserOptions = {
        ...this.config.browserOptions,
        args: [...this.config.browserOptions.args, ...proxyArgs],
      };

      // Launch browser
      this.browser = await puppeteer.launch(browserOptions);
      
      // Create new page
      this.page = await this.browser.newPage();

      // Apply stealth measures
      await this.stealthUtils.applyStealthMeasures(this.page, this.config);

      // Set up request interception for additional stealth
      await this.page.setRequestInterception(true);
      this.page.on('request', this.handleRequest.bind(this));

      // Set up response monitoring
      this.page.on('response', this.handleResponse.bind(this));

      // Set up console logging
      this.page.on('console', msg => {
        if (this.config.logging.console) {
          console.log(`PAGE LOG: ${msg.text()}`);
        }
      });

      // Try to load cookies if enabled
      if (this.config.cookies.enabled && this.config.cookies.autoLoad) {
        await this.loadCookiesIfAvailable();
      }

      console.log('Browser initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize browser:', error);
      throw error;
    }
  }

  /**
   * Handle requests for rate limiting and filtering
   */
  async handleRequest(request) {
    // Rate limiting
    // await this.enforceRateLimit();

    // Block unnecessary resources for faster loading
    const resourceType = request.resourceType();
    if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
      request.abort();
      return;
    }

    // Add random delays to requests
    if (Math.random() < 0.1) { // 10% chance
      await this.stealthUtils.sleep(this.stealthUtils.randomDelay(100, 500));
    }

    request.continue();
  }

  /**
   * Handle responses for monitoring
   */
  handleResponse(response) {
    const status = response.status();
    const url = response.url();

    if (status >= 400) {
      console.warn(`HTTP ${status} for ${url}`);
    }

    // Check for bot detection
    if (status === 429 || url.includes('checkpoint') || url.includes('captcha')) {
      console.warn('Possible bot detection detected!');
    }
  }

  /**
   * Enforce rate limiting
   */
  async enforceRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    // Reset counter if minute has passed
    if (timeSinceLastRequest > 60000) {
      this.requestCount = 0;
    }

    // Check rate limit
    if (this.requestCount >= this.config.rateLimit.requestsPerMinute) {
      const waitTime = 60000 - timeSinceLastRequest;
      console.log(`Rate limit reached, waiting ${waitTime}ms`);
      await this.stealthUtils.sleep(waitTime);
      this.requestCount = 0;
    }

    this.requestCount++;
    this.lastRequestTime = now;
  }

  /**
   * Navigate to Facebook with stealth
   */
  async navigateToFacebook() {
    try {
      console.log('Navigating to Facebook...');
      
      await this.page.goto(this.config.facebook.baseUrl, {
        waitUntil: 'networkidle2',
        timeout: 30000,
      });

      // Random delay after page load
      const delay = this.stealthUtils.randomDelay(
        this.config.delays.pageLoad.min,
        this.config.delays.pageLoad.max
      );
      await this.stealthUtils.sleep(delay);

      // Random scroll to simulate human behavior
      await this.stealthUtils.randomScroll(this.page);

      console.log('Successfully navigated to Facebook');
      return true;
    } catch (error) {
      console.error('Failed to navigate to Facebook:', error);
      throw error;
    }
  }

  /**
   * Login to Facebook with human-like behavior
   */
  async login(email, password) {
    try {
      console.log('Attempting to login to Facebook...');

      // Navigate to login page if not already there
      if (!this.page.url().includes('facebook.com')) {
        await this.navigateToFacebook();
      }

      // Check if already logged in
      if (await this.checkIfLoggedIn()) {
        console.log('Already logged in');
        this.isLoggedIn = true;
        return true;
      }

      // Navigate to login page
      await this.page.goto(this.config.facebook.loginUrl, {
        waitUntil: 'networkidle2',
      });

      // Wait for login form
      await this.page.waitForSelector(this.config.facebook.selectors.emailInput);

      // Human-like typing for email
      await this.stealthUtils.humanType(
        this.page,
        this.config.facebook.selectors.emailInput,
        email
      );

      // Random delay between fields
      await this.stealthUtils.sleep(this.stealthUtils.randomDelay(500, 1500));

      // Human-like typing for password
      await this.stealthUtils.humanType(
        this.page,
        this.config.facebook.selectors.passwordInput,
        password
      );

      // Random delay before clicking login
      await this.stealthUtils.sleep(this.stealthUtils.randomDelay(1000, 2000));

      // Human-like click on login button
      await this.stealthUtils.humanClick(
        this.page,
        this.config.facebook.selectors.loginButton
      );

      // Wait for navigation
      await this.page.waitForNavigation({ waitUntil: 'networkidle2' });

      // Check if login was successful
      if (await this.checkIfLoggedIn()) {
        console.log('Login successful');
        this.isLoggedIn = true;

        // Auto-save cookies if enabled
        if (this.config.cookies.enabled && this.config.cookies.autoSave) {
          await this.saveCookies();
        }

        return true;
      } else {
        throw new Error('Login failed - check credentials or captcha required');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * Check if user is logged in
   */
  async checkIfLoggedIn() {
    try {
      // Use cookie manager's more comprehensive check
      if (this.cookieManager) {
        return await this.cookieManager.checkLoginStatus(this.page);
      }

      // Fallback to simple check
      const profileLink = await this.page.$(this.config.facebook.selectors.profileLink);
      return !!profileLink;
    } catch (error) {
      return false;
    }
  }

  /**
   * Save current session cookies to file
   */
  async saveCookies(filename = null) {
    try {
      if (!this.config.cookies.enabled) {
        console.warn('Cookie management is disabled');
        return { success: false, error: 'Cookie management disabled' };
      }

      const result = await this.cookieManager.saveCookies(this.page, filename);
      if (result.success) {
        console.log(`🍪 Cookies saved successfully: ${result.count} cookies`);
      }
      return result;
    } catch (error) {
      console.error('Failed to save cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Load cookies from file and inject into current session
   */
  async loadCookies(filename = null) {
    try {
      if (!this.config.cookies.enabled) {
        console.warn('Cookie management is disabled');
        return { success: false, error: 'Cookie management disabled' };
      }

      const result = await this.cookieManager.loadAndInjectCookies(this.page, filename);
      if (result.success) {
        console.log(`🍪 Cookies loaded successfully: ${result.loaded} cookies`);

        // Check if cookies are valid
        if (this.config.cookies.validateOnLoad) {
          const isLoggedIn = await this.checkIfLoggedIn();
          if (isLoggedIn) {
            this.isLoggedIn = true;
            console.log('✅ Cookie authentication successful');
          } else {
            console.warn('⚠️ Cookies loaded but authentication failed');
          }
        }
      }
      return result;
    } catch (error) {
      console.error('Failed to load cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Try to load cookies automatically if available
   */
  async loadCookiesIfAvailable() {
    try {
      const result = await this.loadCookies();
      if (result.success && this.isLoggedIn) {
        console.log('🚀 Automatically authenticated using saved cookies');
        return true;
      }
      return false;
    } catch (error) {
      console.log('No valid cookies found, will need to login manually');
      return false;
    }
  }

  /**
   * Authenticate using cookies (load and validate)
   */
  async authenticateWithCookies(filename = null) {
    try {
      console.log('🍪 Attempting cookie authentication...');

      const loadResult = await this.loadCookies(filename);
      if (!loadResult.success) {
        return { success: false, error: 'Failed to load cookies', details: loadResult };
      }

      // Navigate to Facebook to test authentication
      await this.navigateToFacebook();

      // Check if authentication was successful
      const isLoggedIn = await this.checkIfLoggedIn();
      if (isLoggedIn) {
        this.isLoggedIn = true;
        console.log('✅ Cookie authentication successful');
        return { success: true, message: 'Authenticated successfully with cookies' };
      } else {
        console.warn('❌ Cookie authentication failed - cookies may be expired');
        return { success: false, error: 'Authentication failed - cookies expired or invalid' };
      }
    } catch (error) {
      console.error('Cookie authentication error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * List available cookie files
   */
  async listCookieFiles() {
    try {
      return await this.cookieManager.listCookieFiles();
    } catch (error) {
      console.error('Failed to list cookie files:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a cookie file
   */
  async deleteCookieFile(filename) {
    try {
      return await this.cookieManager.deleteCookieFile(filename);
    } catch (error) {
      console.error('Failed to delete cookie file:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Export cookies in different formats
   */
  async exportCookies(format = 'json', filename = null) {
    try {
      return await this.cookieManager.exportCookies(this.page, format, filename);
    } catch (error) {
      console.error('Failed to export cookies:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Manually inject cookies (for when you copy cookies from browser)
   */
  async injectCookiesManually(cookiesArray) {
    try {
      console.log('🍪 Manually injecting cookies...');

      const result = await this.cookieManager.injectCookies(this.page, cookiesArray);
      if (result.success) {
        console.log(`✅ Manually injected ${result.count} cookies`);

        // Navigate to Facebook to test
        await this.navigateToFacebook();

        // Check authentication
        const isLoggedIn = await this.checkIfLoggedIn();
        if (isLoggedIn) {
          this.isLoggedIn = true;
          console.log('✅ Manual cookie injection successful');

          // Optionally save these cookies for future use
          if (this.config.cookies.autoSave) {
            await this.saveCookies('manual_cookies.json');
          }
        }

        return { success: true, authenticated: isLoggedIn };
      }
      return result;
    } catch (error) {
      console.error('Failed to inject cookies manually:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Close browser and cleanup
   */
  async close() {
    try {
      if (this.page) {
        await this.page.close();
      }
      if (this.browser) {
        await this.browser.close();
      }
      console.log('Browser closed successfully');
    } catch (error) {
      console.error('Error closing browser:', error);
    }
  }

  /**
   * Get current page screenshot for debugging
   */
  async screenshot(filename = 'debug.png') {
    if (this.page) {
      await this.page.screenshot({ path: `./logs/${filename}`, fullPage: true });
      console.log(`Screenshot saved: ./logs/${filename}`);
    }
  }
}

module.exports = FacebookCrawler;
